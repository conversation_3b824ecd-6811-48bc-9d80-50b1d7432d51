[gd_scene load_steps=2 format=3 uid="uid://c8xvn2qkl4fh8"]

[ext_resource type="Script" uid="uid://cwf7m52emyw0" path="res://Script/UI/inventory_slot.gd" id="1_slot"]

[node name="InventorySlot" type="Button"]
custom_minimum_size = Vector2(64, 64)
flat = true
script = ExtResource("1_slot")

[node name="Background" type="Panel" parent="."]
unique_name_in_owner = true
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2

[node name="ItemIcon" type="TextureRect" parent="."]
unique_name_in_owner = true
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 4.0
offset_top = 4.0
offset_right = -4.0
offset_bottom = -20.0
grow_horizontal = 2
grow_vertical = 2
expand_mode = 1
stretch_mode = 5

[node name="QuantityLabel" type="Label" parent="."]
unique_name_in_owner = true
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -20.0
offset_top = -16.0
grow_horizontal = 0
grow_vertical = 0
text = "99"
horizontal_alignment = 2
vertical_alignment = 2

[node name="RarityBorder" type="NinePatchRect" parent="."]
unique_name_in_owner = true
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
